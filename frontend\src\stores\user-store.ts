/**
 * Handles app user related data. App user is this current user that is logged in and using this app.
 */

import { defineStore } from 'pinia';
import { useApi } from 'src/api';

let timerBalance = -1;
const timerBalanceInterval = 60000;

const api = useApi();

export const useUserStore = defineStore('user', {
  state: () => ({
    //Variables in storage will be saved to local browser storage
    storage: {
      walletAddress: '',
      baseAddress: '',
      secret: '',
      passphrase: '',
      apiKey: '',
      polySessionCookie: '', //Needed for certain features like comment posting
    },
    hasLoaded: false,
    username: '',
    profileImage: undefined as string | undefined,
    balance: 0,
  }),
  //Note: Getters are reactive (computed) properties
  getters: {
    isUserSetup(): boolean {
      return !!this.storage.walletAddress;
    },
  },
  actions: {
    async fetchUserBalance() {
      const info = await api.getUserBalance();
      this.balance = info.balance;

      if (timerBalance !== -1) {
        clearInterval(timerBalance);
      }
      timerBalance = setInterval(this.fetchUserBalance, timerBalanceInterval) as unknown as number;
    },
    async fetchUserProfile() {
      const profile = await api.getUserProfile(this.storage.baseAddress);
      this.username = profile.name;
      this.profileImage = profile.profileImage;
      this.hasLoaded = true;
    },
    async loadStorage() {
      let sessionData = null;
      let isValid = false;

      try {
        sessionData = JSON.parse(localStorage.getItem('user')!);
      }
      catch (e) {
      }

      Object.assign(this.storage, sessionData);

      //DEBUG
      //patch in keys from server setup for now
      const pks = await api.getPrivateKeys();
      this.setUser(pks.walletAddress, pks.baseAddress, pks.secret, pks.passphrase, pks.apiKey);
    },
    setUser(walletAddress: string, baseAddress: string, secret: string, passphrase: string, apiKey: string) {
      this.storage = {
        walletAddress: walletAddress,
        baseAddress: baseAddress,
        secret: secret,
        passphrase: passphrase,
        apiKey: apiKey,
        polySessionCookie: this.storage.polySessionCookie ?? '',
      };
      //Save to session store
      this.saveToLocalStorage();
    },
    setPolySessionCookie(cookie: string) {
      this.storage.polySessionCookie = cookie;
      this.saveToLocalStorage();
    },
    clearPolySessionCookie() {
      this.storage.polySessionCookie = '';
      this.saveToLocalStorage();
    },
    clearUser() {
      this.$reset();
      localStorage.removeItem('user');
    },
    setBalance(balance: number) {
      this.balance = balance;
    },
    saveToLocalStorage() {
      localStorage.setItem('user', JSON.stringify(this.storage));
    },
  }
});
