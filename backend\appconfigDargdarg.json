{
  // Don't forget to escape backslashes for directories with \\
  "serverPort": 3000,
  "clientUrl": "http://localhost:9000",
  "url": "http://localhost:3000",
  "dbConnectionString": "postgres://postgres:test@localhost/PolyEnhanced",
  //"polyProxy": "*********************************************************",
  "polyProxy": "*********************************************************",
  "polyProxyWallet": "******************************************",
  "polyBaseWallet": "******************************************",
  "privateKey": "0x599a18953c5bbae4c49fc2db89466641f8d7ca3b1d1fa429fc5b36e709c84f28",
  "polyCredsApiKey": "37ef654b-c2b8-3dd0-a697-ce01d001fc43",
  "polyCredsSecret": "d1LihO6oTeA5Z7ljTpN6dhI9JMIQWOGF-aKe_MTtqCI=",
  "polyCredsPassphrase": "****************************************************************",
  "polyClobSignatureType": 1,
  "polyClobApi": "https://clob.polymarket.com",
  "polyGammaApi": "https://gamma-api.polymarket.com",
  "polyDataApi": "https://data-api.polymarket.com",
  "tweetCounterPollRateMs": 60000,
}
