import { ApiHistoryItem, PolyGammaHistoryItem, PolyUserBalanceResponse } from '@shared/api-dataclasses-shared';
import express, { NextFunction, Request, Response } from 'express';
import { catchAsync, enableAxiosProxy, getPolyClient } from '@src/utils';

const router = express.Router();

router.get('/positions', catchAsync(async (req: Request, res: Response) => {
  const userParam = req.query.user as (string | undefined);
  const marketsParam = req.query.markets as (string | undefined);

  if (!userParam) {
    res.status(400).send({ error: 'Missing user' });
    return;
  }

  const markets: string[] = [];
  if (marketsParam) {
    markets.push(...marketsParam.split(","));
  }

  //Get data for event
  const client = getPolyClient();
  const positionData = await client.getPositions(userParam, markets)

  res.send(positionData);
}));

router.get('/orders', catchAsync(async (req: Request, res: Response) => {
  const data = await getPolyClient().getOrders();

  res.send(data);
}));

router.post('/orders', catchAsync(async (req: Request, res: Response) => {
  let { assetId, price, shares, isBuy, tick } = req.body;

  if (!assetId || !Number(price) || !Number(shares) || isBuy === undefined) {
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  console.log(assetId, price, shares, isBuy, tick);
  const client = getPolyClient();
  const resData = await client.placeOrder(assetId, Number(price), Number(shares), isBuy, tick);

  if (resData.error) {
    resData.errorMsg = resData.error;
    resData.success = false;
    resData.error = undefined;
  }
  res.send(resData);
}));

router.delete('/orders', catchAsync(async (req: Request, res: Response) => {
  const { orderId } = req.body;

  if (!orderId) {
    console.log(req.body);
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  const orderIds = orderId.split(",");

  const client = getPolyClient();
  const resData = await client.cancelOrders(orderIds);

  //console.log(resData);
  res.send(resData);
}));

router.get('/balance', catchAsync(async (req: Request, res: Response) => {
  const client = getPolyClient();
  const balance = await client.getBalance();

  res.send({ balance: balance } as PolyUserBalanceResponse);
}));

router.get('/profile', catchAsync(async (req: Request, res: Response) => {
  const { userAddress } = req.query as Record<string, string | undefined>;

  if (!userAddress) {
    console.log(req.body);
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  const client = getPolyClient();
  const resData = await client.getUserProfile(userAddress);

  res.send(resData);
}));

router.post('/history', catchAsync(async (req: Request, res: Response) => {
  let { proxyWallet, markets } = req.body as Record<string, string | undefined>;

  if (!proxyWallet || !markets) {
    console.log(req.body);
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  proxyWallet = proxyWallet.toLowerCase();
  const marketsArr = markets.split(",");
  //Run history update to push latest poly activity to db
  await global.polyUserHistoryModel.updateUserHistoryFromPoly(getPolyClient(), proxyWallet, marketsArr);
  //Get full db history matching market ids
  const historyData = await global.polyUserHistoryModel.getUserMarketHistory(proxyWallet, marketsArr);

  res.send(historyData as ApiHistoryItem[]);
}));

router.get('/profit', catchAsync(async (req: Request, res: Response) => {
  const { proxyWallet } = req.query as Record<string, string | undefined>;

  if (!proxyWallet) {
    console.log(req.body);
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  const client = getPolyClient();
  const profit = await client.getUserProfit(proxyWallet);

  res.send({ profit });
}));

export default router;
